// 购物车状态管理 - 使用Pinia
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import http from '@/utils/http.js'

export const useCartStore = defineStore('cart', () => {
  // 购物车商品列表
  const cartItems = ref([])

  // 计算是否全选
  const isAllSelected = computed(() => {
    return cartItems.value.length > 0 && cartItems.value.every(item => item.isChecked)
  })

  // 计算选中商品的总价
  const selectedItemsPrice = computed(() => {
    return cartItems.value
      .filter(item => item.isChecked)
      .reduce((total, item) => total + (item.price * item.goodsNum), 0)
  })

  // 计算选中商品的数量
  const selectedItemsCount = computed(() => {
    return cartItems.value
      .filter(item => item.isChecked)
      .reduce((total, item) => total + item.goodsNum, 0)
  })

  // 添加商品到购物车
  const addToCart = async (goods, quantity = 1) => {
    try {
      // 检查商品是否已在购物车中
      const existingItem = cartItems.value.find(item => item.id === goods.id)

      if (existingItem) {
        // 如果已存在，增加数量
        existingItem.goodsNum += quantity
      } else {
        // 如果不存在，添加新商品
        const newItem = {
          ...goods,
          goodsNum: quantity,
          isChecked: true
        }
        cartItems.value.push(newItem)
      }

      // 同步到服务器
      await syncCartToServer()

      return true
    } catch (error) {
      console.error('添加到购物车失败:', error)
      return false
    }
  }

  // 从购物车移除商品
  const removeFromCart = async (goodsId) => {
    try {
      const index = cartItems.value.findIndex(item => item.id === goodsId)
      if (index > -1) {
        cartItems.value.splice(index, 1)
        await syncCartToServer()
      }
      return true
    } catch (error) {
      console.error('从购物车移除失败:', error)
      return false
    }
  }

  // 更新商品数量
  const updateQuantity = async (goodsId, quantity) => {
    try {
      if (quantity < 1) return false

      const item = cartItems.value.find(item => item.id === goodsId)
      if (item) {
        item.goodsNum = quantity
        await syncCartToServer()
        return true
      }
      return false
    } catch (error) {
      console.error('更新数量失败:', error)
      return false
    }
  }

  // 增加商品数量
  const increaseQuantity = async (goodsId) => {
    const item = cartItems.value.find(item => item.id === goodsId)
    if (item) {
      return await updateQuantity(goodsId, item.goodsNum + 1)
    }
    return false
  }

  // 减少商品数量
  const decreaseQuantity = async (goodsId) => {
    const item = cartItems.value.find(item => item.id === goodsId)
    if (item && item.goodsNum > 1) {
      return await updateQuantity(goodsId, item.goodsNum - 1)
    }
    return false
  }

  // 切换商品选中状态
  const toggleItemCheck = (goodsId) => {
    const item = cartItems.value.find(item => item.id === goodsId)
    if (item) {
      item.isChecked = !item.isChecked
    }
  }

  // 全选/取消全选
  const toggleAllCheck = (checked) => {
    cartItems.value.forEach(item => {
      item.isChecked = checked
    })
  }

  // 清空购物车
  const clearCart = async () => {
    try {
      cartItems.value = []
      await syncCartToServer()
      return true
    } catch (error) {
      console.error('清空购物车失败:', error)
      return false
    }
  }

  // 获取选中的商品
  const getSelectedItems = () => {
    return cartItems.value.filter(item => item.isChecked)
  }

  // 加载购物车数据
  const loadCartData = async () => {
    try {
      const response = await http.get('/cart')
      cartItems.value = response.data || []
      console.log('购物车数据加载成功:', cartItems.value)
    } catch (error) {
      console.error('加载购物车数据失败:', error)
      cartItems.value = []
    }
  }

  // 同步购物车到服务器
  const syncCartToServer = async () => {
    try {
      // 这里简化处理，实际项目中需要根据用户ID来管理购物车
      console.log('同步购物车到服务器:', cartItems.value)
    } catch (error) {
      console.error('同步购物车失败:', error)
    }
  }

  return {
    cartItems,
    isAllSelected,
    selectedItemsPrice,
    selectedItemsCount,
    addToCart,
    toggleAllCheck,
    toggleItemCheck,
    updateQuantity,
    increaseQuantity,
    decreaseQuantity,
    removeFromCart,
    clearCart,
    getSelectedItems,
    loadCartData
  }
})
