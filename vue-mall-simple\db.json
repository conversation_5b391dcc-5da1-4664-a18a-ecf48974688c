{"userInfo": [{"id": 1, "username": "12345678901", "nickname": "测试用户", "password": "123456789", "email": "<EMAIL>"}], "categories": [{"id": 1000, "firstSortName": "手机数码", "children": [{"id": 1001, "secondSortName": "手机通讯", "fSortid": 1000, "children": [{"id": 10001, "thirdSortName": "智能手机", "fSortid": 1000, "sSortid": 1001}, {"id": 10002, "thirdSortName": "老人机", "fSortid": 1000, "sSortid": 1001}, {"id": 10003, "thirdSortName": "5G手机", "fSortid": 1000, "sSortid": 1001}]}, {"id": 1002, "secondSortName": "数码配件", "fSortid": 1000, "children": [{"id": 10004, "thirdSortName": "手机壳", "fSortid": 1000, "sSortid": 1002}, {"id": 10005, "thirdSortName": "充电器", "fSortid": 1000, "sSortid": 1002}, {"id": 10006, "thirdSortName": "耳机", "fSortid": 1000, "sSortid": 1002}]}, {"id": 1003, "secondSortName": "平板电脑", "fSortid": 1000, "children": [{"id": 10007, "thirdSortName": "iPad", "fSortid": 1000, "sSortid": 1003}, {"id": 10008, "thirdSortName": "安卓平板", "fSortid": 1000, "sSortid": 1003}]}, {"id": 1004, "secondSortName": "智能穿戴", "fSortid": 1000, "children": [{"id": 10009, "thirdSortName": "智能手表", "fSortid": 1000, "sSortid": 1004}, {"id": 10010, "thirdSortName": "智能手环", "fSortid": 1000, "sSortid": 1004}]}]}, {"id": 2000, "firstSortName": "电脑办公", "children": [{"id": 2001, "secondSortName": "笔记本电脑", "fSortid": 2000, "children": [{"id": 20001, "thirdSortName": "游戏本", "fSortid": 2000, "sSortid": 2001}, {"id": 20002, "thirdSortName": "商务本", "fSortid": 2000, "sSortid": 2001}, {"id": 20003, "thirdSortName": "轻薄本", "fSortid": 2000, "sSortid": 2001}]}, {"id": 2002, "secondSortName": "台式电脑", "fSortid": 2000, "children": [{"id": 20004, "thirdSortName": "组装机", "fSortid": 2000, "sSortid": 2002}, {"id": 20005, "thirdSortName": "品牌机", "fSortid": 2000, "sSortid": 2002}]}, {"id": 2003, "secondSortName": "办公设备", "fSortid": 2000, "children": [{"id": 20006, "thirdSortName": "打印机", "fSortid": 2000, "sSortid": 2003}, {"id": 20007, "thirdSortName": "投影仪", "fSortid": 2000, "sSortid": 2003}]}, {"id": 2004, "secondSortName": "电脑配件", "fSortid": 2000, "children": [{"id": 20008, "thirdSortName": "键盘鼠标", "fSortid": 2000, "sSortid": 2004}, {"id": 20009, "thirdSortName": "显示器", "fSortid": 2000, "sSortid": 2004}]}]}, {"id": 3000, "firstSortName": "家用电器", "children": [{"id": 3001, "secondSortName": "大家电", "fSortid": 3000, "children": [{"id": 30001, "thirdSortName": "冰箱", "fSortid": 3000, "sSortid": 3001}, {"id": 30002, "thirdSortName": "洗衣机", "fSortid": 3000, "sSortid": 3001}, {"id": 30003, "thirdSortName": "空调", "fSortid": 3000, "sSortid": 3001}]}, {"id": 3002, "secondSortName": "厨房电器", "fSortid": 3000, "children": [{"id": 30004, "thirdSortName": "微波炉", "fSortid": 3000, "sSortid": 3002}, {"id": 30005, "thirdSortName": "电饭煲", "fSortid": 3000, "sSortid": 3002}]}, {"id": 3003, "secondSortName": "生活电器", "fSortid": 3000, "children": [{"id": 30006, "thirdSortName": "吸尘器", "fSortid": 3000, "sSortid": 3003}, {"id": 30007, "thirdSortName": "净化器", "fSortid": 3000, "sSortid": 3003}]}]}, {"id": 4000, "firstSortName": "服装鞋包", "children": [{"id": 4001, "secondSortName": "男装", "fSortid": 4000, "children": [{"id": 40001, "thirdSortName": "T恤", "fSortid": 4000, "sSortid": 4001}, {"id": 40002, "thirdSortName": "衬衫", "fSortid": 4000, "sSortid": 4001}, {"id": 40003, "thirdSortName": "外套", "fSortid": 4000, "sSortid": 4001}]}, {"id": 4002, "secondSortName": "女装", "fSortid": 4000, "children": [{"id": 40004, "thirdSortName": "连衣裙", "fSortid": 4000, "sSortid": 4002}, {"id": 40005, "thirdSortName": "上衣", "fSortid": 4000, "sSortid": 4002}]}, {"id": 4003, "secondSortName": "鞋靴", "fSortid": 4000, "children": [{"id": 40006, "thirdSortName": "运动鞋", "fSortid": 4000, "sSortid": 4003}, {"id": 40007, "thirdSortName": "休闲鞋", "fSortid": 4000, "sSortid": 4003}]}, {"id": 4004, "secondSortName": "箱包", "fSortid": 4000, "children": [{"id": 40008, "thirdSortName": "背包", "fSortid": 4000, "sSortid": 4004}, {"id": 40009, "thirdSortName": "手提包", "fSortid": 4000, "sSortid": 4004}]}]}, {"id": 5000, "firstSortName": "运动户外", "children": [{"id": 5001, "secondSortName": "运动器材", "fSortid": 5000, "children": [{"id": 50001, "thirdSortName": "健身器材", "fSortid": 5000, "sSortid": 5001}, {"id": 50002, "thirdSortName": "球类运动", "fSortid": 5000, "sSortid": 5001}]}, {"id": 5002, "secondSortName": "户外装备", "fSortid": 5000, "children": [{"id": 50003, "thirdSortName": "帐篷", "fSortid": 5000, "sSortid": 5002}, {"id": 50004, "thirdSortName": "登山包", "fSortid": 5000, "sSortid": 5002}]}, {"id": 5003, "secondSortName": "运动服饰", "fSortid": 5000, "children": [{"id": 50005, "thirdSortName": "运动套装", "fSortid": 5000, "sSortid": 5003}, {"id": 50006, "thirdSortName": "运动鞋", "fSortid": 5000, "sSortid": 5003}]}]}], "goods": [{"id": 1, "name": "华为 Mate 60 Pro 智能手机", "price": 6999, "oldPrice": 7999, "imgSrc": ["/images/phone-huawei.svg", "/images/phone-huawei.svg"], "promotions": "限时特价，立减1000元", "category": {"fSortid": 1000, "sSortid": 1001, "tSortid": 10001}}, {"id": 2, "name": "苹果 iPhone 15 Pro Max", "price": 9999, "oldPrice": 10999, "imgSrc": ["/images/phone-iphone.svg", "/images/phone-iphone.svg"], "promotions": "新品上市，享12期免息", "category": {"fSortid": 1000, "sSortid": 1001, "tSortid": 10001}}, {"id": 3, "name": "小米13 Ultra 旗舰手机", "price": 5999, "oldPrice": 6999, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"], "promotions": "徕卡影像，专业摄影", "category": {"fSortid": 1000, "sSortid": 1001, "tSortid": 10001}}, {"id": 4, "name": "联想ThinkPad X1 Carbon", "price": 12999, "oldPrice": 14999, "imgSrc": ["/images/laptop-thinkpad.svg", "/images/laptop-thinkpad.svg"], "promotions": "商务首选，轻薄便携", "category": {"fSortid": 2000, "sSortid": 2001, "tSortid": 20001}}, {"id": 5, "name": "OPPO Find X6 Pro 5G手机", "price": 4999, "oldPrice": 5999, "imgSrc": ["/images/phone-huawei.svg", "/images/phone-huawei.svg"], "promotions": "哈苏影像，拍照旗舰", "category": {"fSortid": 1000, "sSortid": 1001, "tSortid": 10003}}, {"id": 6, "name": "vivo X90 Pro+ 智能手机", "price": 5499, "oldPrice": 6299, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"], "promotions": "蔡司光学，专业影像", "category": {"fSortid": 1000, "sSortid": 1001, "tSortid": 10001}}, {"id": 7, "name": "荣耀Magic5 Pro 手机", "price": 4699, "oldPrice": 5199, "imgSrc": ["/images/phone-iphone.svg", "/images/phone-iphone.svg"], "promotions": "鹰眼相机，夜拍神器", "category": {"fSortid": 1000, "sSortid": 1001, "tSortid": 10001}}, {"id": 8, "name": "苹果 MacBook Pro 14英寸", "price": 15999, "oldPrice": 17999, "imgSrc": ["/images/laptop-thinkpad.svg", "/images/laptop-thinkpad.svg"], "promotions": "M3芯片，性能强劲", "category": {"fSortid": 2000, "sSortid": 2001, "tSortid": 20002}}, {"id": 9, "name": "戴尔 XPS 13 轻薄本", "price": 8999, "oldPrice": 9999, "imgSrc": ["/images/laptop-thinkpad.svg", "/images/laptop-thinkpad.svg"], "promotions": "超窄边框，便携办公", "category": {"fSortid": 2000, "sSortid": 2001, "tSortid": 20003}}, {"id": 10, "name": "华硕 ROG 游戏本", "price": 11999, "oldPrice": 13999, "imgSrc": ["/images/laptop-thinkpad.svg", "/images/laptop-thinkpad.svg"], "promotions": "RTX4060显卡，游戏利器", "category": {"fSortid": 2000, "sSortid": 2001, "tSortid": 20001}}, {"id": 11, "name": "iPad Pro 12.9英寸", "price": 8999, "oldPrice": 9999, "imgSrc": ["/images/phone-iphone.svg", "/images/phone-iphone.svg"], "promotions": "M2芯片，专业创作", "category": {"fSortid": 1000, "sSortid": 1003, "tSortid": 10007}}, {"id": 12, "name": "小米平板6 Pro", "price": 2999, "oldPrice": 3499, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"], "promotions": "144Hz高刷，影音娱乐", "category": {"fSortid": 1000, "sSortid": 1003, "tSortid": 10008}}, {"id": 13, "name": "Apple Watch Series 9", "price": 2999, "oldPrice": 3299, "imgSrc": ["/images/phone-iphone.svg", "/images/phone-iphone.svg"], "promotions": "健康监测，智能助手", "category": {"fSortid": 1000, "sSortid": 1004, "tSortid": 10009}}, {"id": 14, "name": "华为 Watch GT 4", "price": 1688, "oldPrice": 1988, "imgSrc": ["/images/phone-huawei.svg", "/images/phone-huawei.svg"], "promotions": "长续航，运动监测", "category": {"fSortid": 1000, "sSortid": 1004, "tSortid": 10009}}, {"id": 15, "name": "小米手环8", "price": 299, "oldPrice": 399, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"], "promotions": "性价比之选，健康管理", "category": {"fSortid": 1000, "sSortid": 1004, "tSortid": 10010}}, {"id": 16, "name": "AirPods Pro 2代", "price": 1899, "oldPrice": 2199, "imgSrc": ["/images/phone-iphone.svg", "/images/phone-iphone.svg"], "promotions": "主动降噪，音质出众", "category": {"fSortid": 1000, "sSortid": 1002, "tSortid": 10006}}, {"id": 17, "name": "华为 FreeBuds Pro 3", "price": 1199, "oldPrice": 1399, "imgSrc": ["/images/phone-huawei.svg", "/images/phone-huawei.svg"], "promotions": "智慧降噪，HiFi音质", "category": {"fSortid": 1000, "sSortid": 1002, "tSortid": 10006}}, {"id": 18, "name": "小米 67W 无线充电器", "price": 199, "oldPrice": 299, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"], "promotions": "快充技术，安全便捷", "category": {"fSortid": 1000, "sSortid": 1002, "tSortid": 10005}}, {"id": 19, "name": "海尔 BCD-470WDPG 冰箱", "price": 3999, "oldPrice": 4999, "imgSrc": ["/images/laptop-thinkpad.svg", "/images/laptop-thinkpad.svg"], "promotions": "变频节能，大容量", "category": {"fSortid": 3000, "sSortid": 3001, "tSortid": 30001}}, {"id": 20, "name": "美的 MG80V50D 洗衣机", "price": 2199, "oldPrice": 2699, "imgSrc": ["/images/laptop-thinkpad.svg", "/images/laptop-thinkpad.svg"], "promotions": "智能洗涤，除菌护衣", "category": {"fSortid": 3000, "sSortid": 3001, "tSortid": 30002}}, {"id": 21, "name": "格力 KFR-35GW 空调", "price": 2899, "oldPrice": 3399, "imgSrc": ["/images/laptop-thinkpad.svg", "/images/laptop-thinkpad.svg"], "promotions": "变频节能，静音舒适", "category": {"fSortid": 3000, "sSortid": 3001, "tSortid": 30003}}, {"id": 22, "name": "松下 NN-GF574M 微波炉", "price": 899, "oldPrice": 1199, "imgSrc": ["/images/laptop-thinkpad.svg", "/images/laptop-thinkpad.svg"], "promotions": "光波加热，营养保鲜", "category": {"fSortid": 3000, "sSortid": 3002, "tSortid": 30004}}, {"id": 23, "name": "苏泊尔 CFXB40FC832 电饭煲", "price": 399, "oldPrice": 599, "imgSrc": ["/images/laptop-thinkpad.svg", "/images/laptop-thinkpad.svg"], "promotions": "IH加热，香甜米饭", "category": {"fSortid": 3000, "sSortid": 3002, "tSortid": 30005}}, {"id": 24, "name": "戴森 V15 Detect 吸尘器", "price": 4999, "oldPrice": 5999, "imgSrc": ["/images/laptop-thinkpad.svg", "/images/laptop-thinkpad.svg"], "promotions": "激光探测，深度清洁", "category": {"fSortid": 3000, "sSortid": 3003, "tSortid": 30006}}, {"id": 25, "name": "小米 空气净化器 Pro H", "price": 1699, "oldPrice": 1999, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"], "promotions": "HEPA滤网，智能净化", "category": {"fSortid": 3000, "sSortid": 3003, "tSortid": 30007}}, {"id": 26, "name": "优衣库 男士纯棉T恤", "price": 99, "oldPrice": 149, "imgSrc": ["/images/phone-huawei.svg", "/images/phone-huawei.svg"], "promotions": "舒适透气，经典百搭", "category": {"fSortid": 4000, "sSortid": 4001, "tSortid": 40001}}, {"id": 27, "name": "海澜之家 商务衬衫", "price": 199, "oldPrice": 299, "imgSrc": ["/images/phone-huawei.svg", "/images/phone-huawei.svg"], "promotions": "免烫面料，商务首选", "category": {"fSortid": 4000, "sSortid": 4001, "tSortid": 40002}}, {"id": 28, "name": "ZARA 女士连衣裙", "price": 299, "oldPrice": 399, "imgSrc": ["/images/phone-iphone.svg", "/images/phone-iphone.svg"], "promotions": "时尚设计，优雅气质", "category": {"fSortid": 4000, "sSortid": 4002, "tSortid": 40004}}, {"id": 29, "name": "耐克 Air Max 运动鞋", "price": 899, "oldPrice": 1199, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"], "promotions": "气垫科技，舒适运动", "category": {"fSortid": 4000, "sSortid": 4003, "tSortid": 40006}}, {"id": 30, "name": "阿迪达斯 三叶草背包", "price": 399, "oldPrice": 599, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"], "promotions": "经典设计，实用耐用", "category": {"fSortid": 4000, "sSortid": 4004, "tSortid": 40008}}], "cart": [{"id": 1, "name": "华为 Mate 60 Pro 智能手机", "price": 6999, "oldPrice": 7999, "imgSrc": ["/images/phone-huawei.svg", "/images/phone-huawei.svg"], "promotions": "限时特价，立减1000元", "category": {"fSortid": 1000, "sSortid": 1001, "tSortid": 10001}, "goodsNum": 1, "isChecked": true, "userId": 1}, {"id": 3, "name": "小米13 Ultra 旗舰手机", "price": 5999, "oldPrice": 6999, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"], "promotions": "徕卡影像，专业摄影", "category": {"fSortid": 1000, "sSortid": 1001, "tSortid": 10001}, "goodsNum": 2, "isChecked": false, "userId": 1}], "orders": [{"id": "ORD202401010001", "time": "2024-01-01 10:30:00", "userId": 1, "totalPrice": 6999, "status": "已完成", "list": [{"id": 1, "name": "华为 Mate 60 Pro 智能手机", "price": 6999, "goodsNum": 1, "imgSrc": ["/images/phone-huawei.svg"]}]}, {"id": "ORD202401020002", "time": "2024-01-02 14:20:00", "userId": 1, "totalPrice": 21998, "status": "待发货", "list": [{"id": 4, "name": "联想ThinkPad X1 Carbon", "price": 12999, "goodsNum": 1, "imgSrc": ["/images/laptop-thinkpad.svg"]}, {"id": 2, "name": "苹果 iPhone 15 Pro Max", "price": 9999, "goodsNum": 1, "imgSrc": ["/images/phone-iphone.svg"]}]}, {"id": "ORD1750083167219675", "time": "2025/6/16 22:12:47", "userId": 1, "totalPrice": 6999, "status": "待支付", "list": [{"id": 1, "name": "华为 Mate 60 Pro 智能手机", "price": 6999, "goodsNum": 1, "imgSrc": ["/images/phone-huawei.svg", "/images/phone-huawei.svg"]}]}, {"id": "ORD1750083204419917", "time": "2025/6/16 22:13:24", "userId": 1, "totalPrice": 18997, "status": "已支付", "list": [{"id": 1, "name": "华为 Mate 60 Pro 智能手机", "price": 6999, "goodsNum": 1, "imgSrc": ["/images/phone-huawei.svg", "/images/phone-huawei.svg"]}, {"id": 3, "name": "小米13 Ultra 旗舰手机", "price": 5999, "goodsNum": 2, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"]}]}, {"id": "ORD1750083300000001", "time": "2025/6/16 22:15:00", "userId": 1, "totalPrice": 9999, "status": "待支付", "list": [{"id": 2, "name": "苹果 iPhone 15 Pro Max", "price": 9999, "goodsNum": 1, "imgSrc": ["/images/phone-iphone.svg", "/images/phone-iphone.svg"]}]}, {"id": "ORD1750084145117009", "time": "2025/6/16 22:29:05", "userId": 1, "totalPrice": 18997, "status": "已支付", "list": [{"id": 1, "name": "华为 Mate 60 Pro 智能手机", "price": 6999, "goodsNum": 1, "imgSrc": ["/images/phone-huawei.svg", "/images/phone-huawei.svg"]}, {"id": 3, "name": "小米13 Ultra 旗舰手机", "price": 5999, "goodsNum": 2, "imgSrc": ["/images/phone-xiaomi.svg", "/images/phone-xiaomi.svg"]}]}]}