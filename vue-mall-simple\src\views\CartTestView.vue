<template>
  <div class="cart-test">
    <h1>购物车功能测试</h1>
    
    <div class="test-section">
      <h2>1. 购物车数据测试</h2>
      <button @click="loadCartData">加载购物车数据</button>
      <button @click="addTestProduct">添加测试商品</button>
      <div class="result">
        <p>购物车商品数量: {{ cartStore.cartItems.length }}</p>
        <p>选中商品总价: ¥{{ cartStore.selectedItemsPrice }}</p>
        <p>选中商品数量: {{ cartStore.selectedItemsCount }}</p>
        <p>全选状态: {{ cartStore.isAllSelected ? '是' : '否' }}</p>
      </div>
    </div>

    <div class="test-section">
      <h2>2. 用户登录状态测试</h2>
      <button @click="checkLoginStatus">检查登录状态</button>
      <div class="result">
        <p>登录状态: {{ userStore.isLoggedIn() ? '已登录' : '未登录' }}</p>
        <p>用户信息: {{ userStore.userInfo?.nickname || '无' }}</p>
      </div>
    </div>

    <div class="test-section">
      <h2>3. 结算功能测试</h2>
      <button @click="testCheckout">测试结算</button>
      <div class="result">
        <p>测试结果将在控制台显示</p>
      </div>
    </div>

    <div class="test-section">
      <h2>4. 购物车商品列表</h2>
      <div v-if="cartStore.cartItems.length > 0">
        <div v-for="item in cartStore.cartItems" :key="item.id" class="cart-item">
          <p><strong>{{ item.name }}</strong></p>
          <p>价格: ¥{{ item.price }}</p>
          <p>数量: {{ item.goodsNum }}</p>
          <p>选中: {{ item.isChecked ? '是' : '否' }}</p>
          <button @click="toggleItemCheck(item.id)">切换选中</button>
          <button @click="removeItem(item.id)">删除</button>
        </div>
      </div>
      <p v-else>购物车为空</p>
    </div>
  </div>
</template>

<script>
import { onMounted } from 'vue'
import { useCartStore } from '@/stores/cart.js'
import { useUserStore } from '@/stores/user.js'
import { useOrderStore } from '@/stores/order.js'
import { showSuccess, showError, showInfo } from '@/utils/message.js'

export default {
  name: 'CartTestView',
  setup() {
    const cartStore = useCartStore()
    const userStore = useUserStore()
    const orderStore = useOrderStore()

    onMounted(async () => {
      await loadCartData()
      userStore.initUserState()
    })

    const loadCartData = async () => {
      await cartStore.loadCartData()
      showInfo(`购物车数据加载完成，共${cartStore.cartItems.length}个商品`)
    }

    const addTestProduct = async () => {
      const testProduct = {
        id: 999,
        name: '测试商品',
        price: 99,
        imgSrc: ['/images/phone-huawei.svg'],
        goodsNum: 1,
        isChecked: true
      }
      
      const success = await cartStore.addToCart(testProduct, 1)
      if (success) {
        showSuccess('测试商品添加成功')
      } else {
        showError('添加失败')
      }
    }

    const checkLoginStatus = () => {
      const isLoggedIn = userStore.isLoggedIn()
      showInfo(`登录状态: ${isLoggedIn ? '已登录' : '未登录'}`)
    }

    const testCheckout = async () => {
      const selectedItems = cartStore.getSelectedItems()
      
      console.log('选中的商品:', selectedItems)
      console.log('用户登录状态:', userStore.isLoggedIn())
      console.log('用户信息:', userStore.userInfo)
      console.log('总价:', cartStore.selectedItemsPrice)

      if (selectedItems.length === 0) {
        showError('没有选中的商品')
        return
      }

      if (!userStore.isLoggedIn()) {
        showError('用户未登录')
        return
      }

      try {
        const orderId = orderStore.generateOrderId()
        const orderData = {
          id: orderId,
          time: new Date().toLocaleString('zh-CN'),
          userId: userStore.userInfo?.id || 1,
          totalPrice: cartStore.selectedItemsPrice,
          status: '测试订单',
          list: selectedItems.map(item => ({
            id: item.id,
            name: item.name,
            price: item.price,
            goodsNum: item.goodsNum,
            imgSrc: item.imgSrc
          }))
        }

        console.log('订单数据:', orderData)

        const createdOrder = await orderStore.createOrder(orderData)
        
        if (createdOrder) {
          showSuccess('测试订单创建成功')
          console.log('创建的订单:', createdOrder)
        } else {
          showError('订单创建失败')
        }
      } catch (error) {
        console.error('测试结算失败:', error)
        showError('测试失败: ' + error.message)
      }
    }

    const toggleItemCheck = (goodsId) => {
      cartStore.toggleItemCheck(goodsId)
      showInfo('商品选中状态已切换')
    }

    const removeItem = async (goodsId) => {
      const success = await cartStore.removeFromCart(goodsId)
      if (success) {
        showSuccess('商品已删除')
      } else {
        showError('删除失败')
      }
    }

    return {
      cartStore,
      userStore,
      orderStore,
      loadCartData,
      addTestProduct,
      checkLoginStatus,
      testCheckout,
      toggleItemCheck,
      removeItem
    }
  }
}
</script>

<style scoped>
.cart-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h2 {
  color: #333;
  margin-bottom: 15px;
}

.test-section button {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-section button:hover {
  background: #0056b3;
}

.result {
  margin-top: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.result p {
  margin: 5px 0;
}

.cart-item {
  padding: 10px;
  margin: 10px 0;
  border: 1px solid #eee;
  border-radius: 4px;
  background: #f9f9f9;
}

.cart-item button {
  margin-right: 5px;
  padding: 4px 8px;
  font-size: 12px;
}
</style>
