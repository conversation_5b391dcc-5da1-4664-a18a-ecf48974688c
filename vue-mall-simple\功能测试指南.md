# Vue3在线商城系统 - 功能测试指南

## 测试环境准备

### 1. 启动服务
```bash
# 启动后端服务（终端1）
cd vue-mall-simple
npx json-server --watch db.json --port 3000

# 启动前端服务（终端2）
cd vue-mall-simple
npm run dev
```

### 2. 测试账号
- **用户名：** 12345678901
- **密码：** 123456789

## 核心功能测试

### 测试1：OrderView.vue错误修复验证 ✅

**测试步骤：**
1. 使用测试账号登录系统
2. 点击导航栏"我的订单"或直接访问 `/order`
3. 检查页面是否正常加载，无控制台错误

**预期结果：**
- 页面正常显示订单列表
- 控制台无 "orderStore is not defined" 错误
- 订单数据正确显示

### 测试2：订单支付功能验证 ✅

**测试步骤：**
1. 在订单列表中找到状态为"待支付"的订单
2. 点击"订单详情"进入订单详情页
3. 确认页面显示"立即支付"按钮（仅待支付订单显示）
4. 点击"立即支付"按钮
5. 在确认对话框中点击"确定"
6. 等待支付处理完成
7. 检查订单状态是否更新为"已支付"

**预期结果：**
- 待支付订单显示支付按钮
- 已支付/已完成订单不显示支付按钮
- 支付过程有加载状态显示
- 支付成功后状态更新为"已支付"
- 支付成功有提示信息

### 测试3：完整购物流程验证 ✅

**测试步骤：**
1. **商品浏览：** 访问首页，浏览商品
2. **加入购物车：** 选择商品，点击"加入购物车"
3. **购物车管理：** 
   - 访问购物车页面
   - 测试全选/取消全选
   - 测试数量增减
   - 测试商品删除
   - 检查价格计算是否正确
4. **订单结算：**
   - 选择商品点击"去结算"
   - 确认订单创建成功
   - 检查订单状态为"待支付"
5. **订单管理：**
   - 查看订单列表
   - 进入订单详情
   - 测试支付功能
   - 验证状态更新

**预期结果：**
- 整个流程无错误
- 数据正确保存到json-server
- 状态转换正确
- 用户反馈及时

### 测试4：订单状态样式验证 ✅

**测试步骤：**
1. 访问订单列表页面
2. 检查不同状态订单的颜色显示
3. 访问订单详情页面
4. 检查订单状态标签的颜色

**预期结果：**
- 待支付：黄色背景
- 已支付：蓝色背景  
- 已发货：橙色背景
- 已完成：绿色背景
- 已取消：红色背景

### 测试5：用户权限控制验证 ✅

**测试步骤：**
1. 未登录状态访问 `/cart` 和 `/order`
2. 检查是否重定向到登录页
3. 登录后检查是否只显示当前用户的数据

**预期结果：**
- 未登录用户被重定向到登录页
- 登录用户只能看到自己的购物车和订单

## 数据验证测试

### 测试6：数据同步验证 ✅

**测试步骤：**
1. 执行购物车操作（添加、修改、删除）
2. 检查 `http://localhost:3000/cart` API数据
3. 执行订单操作（创建、支付、删除）
4. 检查 `http://localhost:3000/orders` API数据

**预期结果：**
- 前端操作实时同步到json-server
- API数据与前端显示一致

## 错误处理测试

### 测试7：网络错误处理 ✅

**测试步骤：**
1. 停止json-server服务
2. 尝试执行各种操作
3. 检查错误提示是否友好

**预期结果：**
- 有适当的错误提示
- 应用不会崩溃
- 用户能理解错误原因

## 响应式设计测试

### 测试8：移动端适配验证 ✅

**测试步骤：**
1. 使用浏览器开发者工具切换到移动端视图
2. 测试各个页面的显示效果
3. 测试功能是否正常

**预期结果：**
- 页面在移动端正常显示
- 功能在移动端正常工作
- 布局适应小屏幕

## 性能测试

### 测试9：页面加载性能 ✅

**测试步骤：**
1. 使用浏览器开发者工具的Performance面板
2. 记录页面加载时间
3. 检查是否有性能瓶颈

**预期结果：**
- 页面加载时间合理
- 无明显性能问题

## 测试数据

### 可用的测试订单ID：
- `ORD1750083167219675` - 待支付状态
- `ORD1750083204419917` - 待支付状态  
- `ORD1750083300000001` - 待支付状态
- `ORD202401020002` - 待发货状态
- `ORD202401010001` - 已完成状态

### 可用的测试商品：
- 华为 Mate 60 Pro 智能手机 (ID: 1)
- 苹果 iPhone 15 Pro Max (ID: 2)
- 小米13 Ultra 旗舰手机 (ID: 3)
- 联想ThinkPad X1 Carbon (ID: 4)

## 测试报告模板

### 测试结果记录：
```
测试项目：[测试名称]
测试时间：[日期时间]
测试结果：✅ 通过 / ❌ 失败
问题描述：[如有问题，详细描述]
修复建议：[如有问题，提供修复建议]
```

## 常见问题排查

### 问题1：页面无法加载
**可能原因：**
- json-server未启动
- 端口冲突
- 网络连接问题

**解决方案：**
- 检查json-server是否在3000端口运行
- 检查前端是否在5173端口运行
- 查看控制台错误信息

### 问题2：订单状态不更新
**可能原因：**
- API请求失败
- 数据格式错误
- 权限问题

**解决方案：**
- 检查网络请求状态
- 验证用户登录状态
- 查看控制台错误信息

### 问题3：支付按钮不显示
**可能原因：**
- 订单状态不是"待支付"
- 组件渲染问题
- 数据加载问题

**解决方案：**
- 确认订单状态为"待支付"
- 检查组件条件渲染逻辑
- 验证订单数据是否正确加载

## 测试完成标准

所有测试项目通过后，系统达到以下标准：
- ✅ 无控制台错误
- ✅ 功能完整可用
- ✅ 用户体验良好
- ✅ 数据同步正确
- ✅ 错误处理完善
- ✅ 响应式设计正常
- ✅ 性能表现良好

完成测试后，Vue3在线商城系统即可投入使用。
