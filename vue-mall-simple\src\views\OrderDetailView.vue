<template>
  <!-- 订单详情页面 -->
  <div class="order-detail-panel">
    <NavView></NavView>
    <div
      class="order-detail-content main-body"
      :style="{ width: globalInfo.bodyWidth + 'px' }"
    >
      <div class="breadcrumb">
        <span @click="goToHome">首页</span>
        <span> > </span>
        <span @click="goToOrder">我的订单</span>
        <span> > </span>
        <span>订单详情</span>
      </div>
      
      <div class="order-info" v-if="orderDetail.id">
        <div class="order-header">
          <h2>订单详情</h2>
          <div class="order-status">
            <span class="status" :class="getStatusClass(orderDetail.status)">
              {{ orderDetail.status || '已完成' }}
            </span>
          </div>
        </div>
        
        <div class="order-basic-info">
          <div class="info-item">
            <label>订单号：</label>
            <span>{{ orderDetail.id }}</span>
          </div>
          <div class="info-item">
            <label>下单时间：</label>
            <span>{{ orderDetail.time }}</span>
          </div>
          <div class="info-item">
            <label>订单金额：</label>
            <span class="price">¥{{ orderDetail.totalPrice }}</span>
          </div>
        </div>
        
        <div class="order-goods">
          <h3>商品清单</h3>
          <div class="goods-list">
            <div 
              class="goods-item" 
              v-for="item in orderDetail.list" 
              :key="item.id"
            >
              <img :src="item.imgSrc[0]" :alt="item.name" />
              <div class="goods-info">
                <h4>{{ item.name }}</h4>
                <div class="goods-details">
                  <span class="quantity">数量：{{ item.goodsNum }}</span>
                  <span class="unit-price">单价：¥{{ item.price }}</span>
                  <span class="subtotal">小计：¥{{ (item.price * item.goodsNum).toFixed(2) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="order-actions">
          <button @click="goToOrder" class="btn-secondary">返回订单列表</button>
          <!-- 支付按钮：仅在订单状态为"待支付"时显示 -->
          <button
            v-if="orderDetail.status === '待支付'"
            @click="payOrder"
            class="btn-pay"
            :disabled="isPaymentProcessing"
          >
            {{ isPaymentProcessing ? '支付中...' : '立即支付' }}
          </button>
          <button @click="contactService" class="btn-primary">联系客服</button>
        </div>
      </div>
      
      <div class="order-not-found" v-else>
        <div class="not-found-content">
          <div class="not-found-icon">📋</div>
          <h3>订单不存在</h3>
          <p>该订单可能已被删除或不存在</p>
          <a href="javascript:;" @click="goToOrder" class="go-back">返回订单列表</a>
        </div>
      </div>
    </div>
    <FooterView></FooterView>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import NavView from '@/components/NavView.vue'
import FooterView from '@/components/FooterView.vue'
import { useUserStore } from '@/stores/user.js'
import { useOrderStore } from '@/stores/order.js'
import { globalInfo } from '@/utils/global.js'
import http from '@/utils/http.js'

export default {
  name: 'OrderDetailView',
  components: {
    NavView,
    FooterView
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const orderStore = useOrderStore()

    // 响应式数据
    const orderDetail = ref({})
    const isPaymentProcessing = ref(false) // 支付处理状态

    // 组件挂载时加载订单详情
    onMounted(async () => {
      await loadOrderDetail()
    })

    // 加载订单详情
    const loadOrderDetail = async () => {
      try {
        const orderId = route.params.id
        const order = await orderStore.getOrderById(orderId)
        if (order) {
          orderDetail.value = order
        } else {
          // 如果订单不存在，显示空状态
          orderDetail.value = {}
        }
      } catch (error) {
        console.error('加载订单详情失败:', error)
        orderDetail.value = {}
      }
    }

    // 跳转到首页
    const goToHome = () => {
      router.push('/')
    }

    // 跳转到订单列表
    const goToOrder = () => {
      router.push('/order')
    }

    // 联系客服
    const contactService = () => {
      alert('客服功能开发中...')
    }

    // 支付订单功能
    const payOrder = async () => {
      if (isPaymentProcessing.value) return

      try {
        isPaymentProcessing.value = true

        // 模拟支付确认
        const confirmPay = confirm(`确认支付 ¥${orderDetail.value.totalPrice} 吗？`)
        if (!confirmPay) {
          isPaymentProcessing.value = false
          return
        }

        // 模拟支付延迟
        await new Promise(resolve => setTimeout(resolve, 1500))

        // 更新订单状态为"已支付"
        const success = await orderStore.updateOrderStatus(orderDetail.value.id, '已支付')

        if (success) {
          // 更新本地订单详情状态
          orderDetail.value.status = '已支付'
          alert('支付成功！订单状态已更新为"已支付"')
        } else {
          alert('支付失败，请重试')
        }
      } catch (error) {
        console.error('支付过程中发生错误:', error)
        alert('支付过程中发生错误，请重试')
      } finally {
        isPaymentProcessing.value = false
      }
    }

    // 获取订单状态样式类
    const getStatusClass = (status) => {
      switch (status) {
        case '待支付':
          return 'status-pending'
        case '已支付':
          return 'status-paid'
        case '已发货':
          return 'status-shipped'
        case '已完成':
          return 'status-completed'
        case '已取消':
          return 'status-cancelled'
        default:
          return 'status-completed'
      }
    }

    return {
      globalInfo,
      orderDetail,
      isPaymentProcessing,
      goToHome,
      goToOrder,
      contactService,
      payOrder,
      getStatusClass
    }
  }
}
</script>

<style scoped>
/* 订单详情页面样式 */
.order-detail-panel {
  min-height: 100vh;
  background: #f5f5f5;
}

.order-detail-content {
  margin: 20px auto;
  background: white;
  border-radius: 8px;
  padding: 30px;
}

.breadcrumb {
  margin-bottom: 30px;
  color: #666;
}

.breadcrumb span {
  cursor: pointer;
}

.breadcrumb span:hover {
  color: #ff6600;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.order-header h2 {
  color: #333;
  margin: 0;
}

.status {
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
}

/* 订单状态样式 */
.status-pending {
  background: #ffc107;
  color: #212529;
}

.status-paid {
  background: #17a2b8;
}

.status-shipped {
  background: #fd7e14;
}

.status-completed {
  background: #28a745;
}

.status-cancelled {
  background: #dc3545;
}

.order-basic-info {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  width: 100px;
  color: #666;
  font-weight: bold;
}

.info-item span {
  color: #333;
}

.price {
  color: #ff6600;
  font-weight: bold;
  font-size: 18px;
}

.order-goods {
  margin-bottom: 30px;
}

.order-goods h3 {
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.goods-item {
  display: flex;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 15px;
}

.goods-item:last-child {
  margin-bottom: 0;
}

.goods-item img {
  width: 100px;
  height: 100px;
  object-fit: contain;
  border-radius: 4px;
  margin-right: 20px;
  background: #f8f8f8;
  padding: 5px;
}

.goods-info {
  flex: 1;
}

.goods-info h4 {
  color: #333;
  margin-bottom: 15px;
  line-height: 1.4;
}

.goods-details {
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 14px;
}

.subtotal {
  color: #ff6600;
  font-weight: bold;
}

.order-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding-top: 30px;
  border-top: 1px solid #e0e0e0;
}

.btn-secondary,
.btn-primary,
.btn-pay {
  padding: 12px 30px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  border: none;
  transition: all 0.3s;
}

.btn-secondary {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e0e0e0;
}

.btn-secondary:hover {
  background: #e9ecef;
}

.btn-primary {
  background: #ff6600;
  color: white;
}

.btn-primary:hover {
  background: #e55a00;
}

/* 支付按钮样式 */
.btn-pay {
  background: #28a745;
  color: white;
  position: relative;
}

.btn-pay:hover:not(:disabled) {
  background: #218838;
}

.btn-pay:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 订单不存在样式 */
.order-not-found {
  padding: 80px 20px;
  text-align: center;
}

.not-found-icon {
  font-size: 80px;
  margin-bottom: 20px;
}

.not-found-content h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.not-found-content p {
  color: #666;
  margin-bottom: 30px;
}

.go-back {
  background: #ff6600;
  color: white;
  padding: 12px 30px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
}

.go-back:hover {
  background: #e55a00;
}

.main-body {
  max-width: 1200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .goods-item {
    flex-direction: column;
  }
  
  .goods-item img {
    width: 100%;
    height: 200px;
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .goods-details {
    flex-direction: column;
    gap: 5px;
  }
  
  .order-actions {
    flex-direction: column;
  }
}
</style>
