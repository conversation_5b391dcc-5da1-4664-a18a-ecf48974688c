<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f0ab06e9-8cb9-4873-ae1e-77e320d79235" name="Changes" comment="在线商城系统">
      <change beforePath="$PROJECT_DIR$/db.json" beforeDir="false" afterPath="$PROJECT_DIR$/db.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/NavView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/NavView.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/router/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/router/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/stores/cart.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/stores/cart.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/CartView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/CartView.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/CategoryDetailView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/CategoryDetailView.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/GoodsDetailView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/GoodsDetailView.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/HomeView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/HomeView.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/OrderDetailView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/OrderDetailView.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/OrderView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/OrderView.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/SearchView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/SearchView.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\Env\apache-maven-3.9.9" />
        <option name="localRepository" value="E:\Env\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\Env\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2yaTyl8Wit8EFlhZRdZixbDS2wR" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "F:/Win/BR/PartTime/2.Processing/6.19 两个 vue 项目 CW 210/online-mall-system/vue-mall-simple",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "reference.settings.project.maven.importing",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f0ab06e9-8cb9-4873-ae1e-77e320d79235" name="Changes" comment="" />
      <created>1746638827820</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746638827820</updated>
      <workItem from="1750071943162" duration="994000" />
      <workItem from="1750082543075" duration="507000" />
    </task>
    <task id="LOCAL-00001" summary="在线商城系统">
      <option name="closed" value="true" />
      <created>1750072011374</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750072011374</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="LOCAL_CHANGES_DETAILS_PREVIEW_SHOWN" value="false" />
    <MESSAGE value="在线商城系统" />
    <option name="LAST_COMMIT_MESSAGE" value="在线商城系统" />
  </component>
</project>